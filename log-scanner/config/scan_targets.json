{"domain_rules": {"sg-pulsar-indigo": {"pattern": "sg-pulsar-indigo{}.bigdata.bigo.inner", "node_count": 50}, "sg10-pulsar-ml": {"pattern": "sg10-pulsar-ml{}.bigdata.bigo.inner", "node_count": 50}, "sg-pulsar-likee": {"pattern": "sg-pulsar-likee{}.bigdata.bigo.inner", "node_count": 50}, "sg-pulsar-live": {"pattern": "sg-pulsar-live{}.bigdata.bigo.inner", "node_count": 50}, "sg-pulsar-common": {"pattern": "sg-pulsar-common{}.bigdata.bigo.inner", "node_count": 50}, "sg10-pulsar-ad": {"pattern": "sg10-pulsar-ad{}.bigdata.bigo.inner", "node_count": 20}, "sg08-pulsar-ad": {"pattern": "sg08-pulsar-ad{}.bigdata.bigo.inner", "node_count": 50}, "sg-pulsar-pre-release": {"pattern": "sg-pulsar-pre-release{}.bigdata.bigo.inner", "node_count": 10}, "sg-pulsar-online-service": {"pattern": "sg-pulsar-online-service{}.bigdata.bigo.inner", "node_count": 10}, "sg-pulsar-imo": {"pattern": "sg-pulsar-imo{}.bigdata.bigo.inner", "node_count": 40}, "sg-pulsar-backup": {"pattern": "sg-buff{}.bigdata.bigo.inner", "node_count": 50}}, "manual_groups": {"bigomq-test-server": ["**************"], "localhost": ["localhost"], "bigomq测试专区": ["*************"], "sg-pulsar-master-service.bigdata.bigo.inner": ["sg-pulsar-master-service.bigdata.bigo.inner"], "sg-pulsar-test": ["hadoop-u20-test2.bigdata.bigo.inner", "sg-hadoop-test1.bigdata.bigo.inner", "hadoop-u20-test1.bigdata.bigo.inner"]}}